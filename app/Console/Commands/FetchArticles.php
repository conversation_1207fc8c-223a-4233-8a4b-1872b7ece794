<?php

namespace App\Console\Commands;

use App\Models\Source;
use Illuminate\Console\Command;
use App\Sources\SicNoticias\SicNoticias;

class FetchArticles extends Command
{
    protected $signature = 'fetch:articles';

    protected $description = 'Fetch articles from sources';

    private const SIC_NOTICIAS = 1;
    private const NOTICIAS_AO_MINUTO = 2;
    private const EXPRESSO = 3;

    public function __construct(
        readonly private Source $source,
        readonly private SicNoticias $sicNoticias
    ) {
        parent::__construct();
    }

    public function handle()
    {
        $sources = $this->source->all();

        foreach ($sources as $source) {
            switch ($source->id) {
                case self::SIC_NOTICIAS:
                    $this->sicNoticias->handle($source);
                    break;
                default:
                    break;
            }
        }
    }
}
