<?php

namespace App\Console\Commands;

use App\Models\Source;
use Illuminate\Console\Command;

class FetchArticles extends Command
{
    protected $signature = 'fetch:articles';

    protected $description = 'Fetch articles from sources';

    private const SIC_NOTICIAS = 1;

    public function __construct(
        readonly private Source $source
    ) {
        parent::__construct();
    }

    public function handle()
    {
        $sources = $this->source->all();

        foreach ($sources as $source) {
            switch ($source->id) {
                case self::SIC_NOTICIAS:
                    
                    break;
                default:
                    break;
            }
        }
    }
}
