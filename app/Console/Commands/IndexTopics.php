<?php

namespace App\Console\Commands;

use App\Models\Topic;
use Illuminate\Console\Command;

class IndexTopics extends Command
{
    protected $signature = 'index:topics';

    protected $description = 'Call externar scout and tntsearch commands to index topics.';

    public function handle()
    {
        $this->call("scout:import", ['model' => Topic::class]);
        $this->call("tntsearch:import", ['model' => Topic::class]);
    }
}
