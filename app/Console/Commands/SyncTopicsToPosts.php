<?php

namespace App\Console\Commands;

use App\Models\Article;
use App\Models\Topic;
use App\Services\OpenAIChat;
use Illuminate\Console\Command;

class SyncTopicsToPosts extends Command
{
    protected $signature = 'sync:topics-to-posts';

    protected $description = 'Sync articles to posts and create a new post';

    public function __construct(
        readonly private Article $articleModel,
        readonly private Topic $topicModel
    ) {
        parent::__construct();
    }

    public function handle()
    {
        $lastTopics = $this->topicModel
            ->whereBetween('created_at', [now()->subHours(12), now()])
            ->select(['name'])
            ->get()
            ->pluck('name')
            ->toArray();

        $lastArticles = $this->articleModel
            ->where('processed', false)
            ->get();

        foreach($lastArticles as $lastArticle) {            
            $topic = OpenAIChat::identifyTopic($lastArticle->title, $lastTopics);

            if (($topic) && (in_array($topic, $lastTopics))) {
                $topic = $this->topicModel
                    ->where('name', $topic)
                    ->first();

                $lastArticle->topic_id = $topic->id;
            }

            $lastArticle->processed = true;
            $lastArticle->save();
        }
    }
}
