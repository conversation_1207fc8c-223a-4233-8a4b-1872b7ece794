<?php

namespace App\Helpers;

use Illuminate\Support\Str;

class Helpers
{
    public static function isResponseValid(?object $response)
    {
        $valid = true;

        $valid &= !is_null($response);

        $valid &= isset($response->title);
        $valid &= isset($response->title->params);
        $valid &= isset($response->title->params->clickbait);
        $valid &= isset($response->title->params->sensacionalismo);
        $valid &= isset($response->title->params->neutralidade);
        $valid &= isset($response->title->reasons);
        $valid &= isset($response->title->reasons->clickbait);
        $valid &= isset($response->title->reasons->sensacionalismo);
        $valid &= isset($response->title->reasons->neutralidade);
        $valid &= isset($response->title->topics);

        $valid &= isset($response->text);
        $valid &= isset($response->text->params);
        $valid &= isset($response->text->params->clareza);
        $valid &= isset($response->text->params->enviesamento);
        $valid &= isset($response->text->params->rigor);
        $valid &= isset($response->text->reasons);
        $valid &= isset($response->text->reasons->clareza);
        $valid &= isset($response->text->reasons->enviesamento);
        $valid &= isset($response->text->reasons->rigor);
        $valid &= isset($response->text->summary);
        $valid &= isset($response->text->entities);

        return !empty($valid);
    }

    public static function generateArticleHTML(array $content)
    {
        $html = collect($content)
            ->map(fn($item) => '<' . $item['fromTag'] . '>' . $item['html'] . '</' . $item['fromTag'] . '>')
            ->toArray();

        return join("", $html);
    } 

    public static function generateSlug(int $id, string $title)
    {
        return trim($id . '-' . Str::slug($title));
    }
}