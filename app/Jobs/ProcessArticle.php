<?php

namespace App\Jobs;

use App\Helpers\Helpers;
use App\Models\Article;
use App\Models\ArticleEntity;
use App\Models\ArticleParameter;
use App\Models\Category;
use App\Models\Entity;
use App\Models\Parameter;
use App\Services\OpenAIChat;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Str;
use App\Models\Source;
use App\Models\Topic;
use App\Prompts\Prompts;
use Illuminate\Support\Facades\DB;

class ProcessArticle implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private array $meta,
        private array $content,
        private Source $sourceModel,
        private Category $categoryModel,
    ) {
        //
    }

    public function handle(): void
    {
        /*
        dump($this->meta);
        dump($this->content);
        dump($this->sourceModel?->toArray());
        dump($this->categoryModel?->toArray());
        dd('-');
        */

        $title = $this->meta['twitter:title'];
        $url = $this->meta['twitter:url'];

        if (Article::query()->where('url', $url)->exists()) {
            return;
        }

        $text = collect($this->content)
            ->filter(fn($item) => $item['fromTag'] == 'p')
            ->map(fn($item) => $item['text'])
            ->toArray();

        $text = join('\n\n', $text);

        $messages = [
            [
                'role' => 'system',
                'content' => 'Você é um assistente que utiliza o idioma português de Portugal e responde SEMPRE no formato JSON especificado.',
            ],
            ['role' => 'user', 'content' => Prompts::getArticle($title, $text)]
        ];

        try {
            $response = OpenAIChat::chat($messages);

            if ($response && isset($response->choices[0])) {                
                $output = json_decode($response->choices[0]->message->content);

                dump($output);

                if (Helpers::isResponseValid($output)) {
                    DB::transaction(function () use ($title, $url, $text, $output) {
                        $article = Article::firstOrNew([
                            'title' => $title,
                            'url' => $url,
                            'source_id' => $this->sourceModel->id,                        
                        ]);
                        $article->category_id = $this->categoryModel->id;
                        $article->description = $this->meta['description'];
                        $article->content_text = $text;
                        $article->cover = $this->meta['twitter:image'];
                        $article->html = Helpers::generateArticleHTML($this->content);
                        $article->summary = $output->text->summary;
                        $article->save();

                        if ($article->id) {
                            $article->slug = Helpers::generateSlug($article->id, $article->title);
                            $article->save();

                            foreach($output->title->params as $key => $value) {
                                $param = Parameter::query()
                                    ->where('name', $key)
                                    ->where('type', 'TITLE')
                                    ->first();
                                
                                $articleParam = ArticleParameter::firstOrNew(['article_id' => $article->id, 'parameter_id' => $param->id]);
                                $articleParam->value = $value;
                                $articleParam->reason = $output->title->reasons->$key;
                                $articleParam->save();
                            }

                            foreach($output->title->topics as $topic) {
                                $topic = Topic::firstOrNew(['name' => $topic]);
                                $topic->slug = Str::slug($topic->name);
                                $topic->save();
                            }

                            foreach($output->text->params as $key => $value) {
                                $param = Parameter::query()
                                    ->where('name', $key)
                                    ->where('type', 'TEXT')
                                    ->first();
                                
                                $articleParam = ArticleParameter::firstOrNew(['article_id' => $article->id, 'parameter_id' => $param->id]);
                                $articleParam->value = $value;
                                $articleParam->reason = $output->text->reasons->$key;
                                $articleParam->save();
                            }

                            foreach($output->text->entities as $entity) {
                                $entity = Entity::firstOrCreate(['name' => $entity]);

                                ArticleEntity::firstOrCreate(['article_id' => $article->id, 'entity_id' => $entity->id]);
                            }

                            return $article->id;
                        }

                        return false;
                    });
                }
            }
        }
        catch(\Exception $e) {
            dump($e->getMessage());
        }
    }
}
