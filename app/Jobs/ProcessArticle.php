<?php

namespace App\Jobs;

use App\Services\OpenAIChat;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ProcessArticle implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $meta,
        public array $content
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $title = $this->meta['twitter:title'];

        $messages = [
            [
                'role' => 'system',
                'content' => 'Be concise. Response ALWAYS in JSON format.',
            ],
            [
                'role' => 'user',
                'content' => 'Interpreta o seguinte título: "' . $title . '" e, numa escala decimal entre 0 a 1, classifica os seguintes parâmetros: clickbait, sensacionalismo, neutralidade e tom emocional. Usa os parâmetros como chaves da resposta JSON.',
            ],
        ];

        $response = OpenAIChat::chat($messages);

        if ($response && isset($response->choices[0])) {
            $output = json_decode($response->choices[0]->message->content);

            dump($output);
        }
    }
}
