<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Watson\Validating\ValidatingTrait;

class Article extends Model
{
    use ValidatingTrait;

    protected $rules = [
		'title'   => 'required',
        'description' => 'required',
        'url' => 'required',
        'content_text' => 'required',
        'cover' => 'required',
        'source_id' => 'required',
        'category_id' => 'required',
        'summary' => 'required',
        'html' => 'required',
	];

    protected $fillable = [
        'title', 
        'description', 
        'url', 
        'content_text', 
        'cover', 
        'slug',
        'source_id', 
        'category_id', 
        'topic_id',
        'summary',
        'html',
        'processed',
    ];

    protected $casts = [
        'summary' => 'array',
        'processed' => 'boolean'
    ];

    public function source()
    {
        return $this->belongsTo(Source::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function topic()
    {
        return $this->belongsTo(Topic::class);
    }
}