<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Watson\Validating\ValidatingTrait;

class ArticleChange extends Model
{
    use ValidatingTrait;

    protected $rules = [
		'article_id'   => 'required',
        'content' => 'required',
	];

    protected $fillable = [
        'article_id', 
        'content',
    ];

    public function article()
    {
        return $this->belongsTo(Article::class);
    }
}
