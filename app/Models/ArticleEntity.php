<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Watson\Validating\ValidatingTrait;

class ArticleEntity extends Model
{
    use ValidatingTrait;

    protected $rules = [
		'article_id'   => 'required',
        'entity_id' => 'required',
	];

    protected $fillable = [
        'article_id', 
        'entity_id',
    ];

    public function article()
    {
        return $this->belongsTo(Article::class);
    }

    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }
}
