<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Watson\Validating\ValidatingTrait;

class ArticleParameter extends Model
{
    use ValidatingTrait;

    protected $table = 'articles_parameters';

    protected $rules = [
		'article_id'   => 'required',
        'parameter_id' => 'required',
        'value' => 'required',
        'reason' => 'required',
	];

    protected $fillable = [
        'article_id', 
        'parameter_id',
        'value',
        'reason'
    ];

    public function article()
    {
        return $this->belongsTo(Article::class);
    }

    public function parameter()
    {
        return $this->belongsTo(Parameter::class);
    }
}
