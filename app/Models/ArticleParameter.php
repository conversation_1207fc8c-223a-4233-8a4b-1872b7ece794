<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Watson\Validating\ValidatingTrait;

class ArticleParameter extends Model
{
    use ValidatingTrait;

    protected $rules = [
		'article_id'   => 'required',
        'parameter_id' => 'required',
        'value' => 'required',
	];

    protected $fillable = [
        'article_id', 
        'parameter_id',
        'value',
    ];

    public function article()
    {
        return $this->belongsTo(Article::class);
    }

    public function parameter()
    {
        return $this->belongsTo(Parameter::class);
    }
}
