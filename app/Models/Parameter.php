<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Watson\Validating\ValidatingTrait;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Parameter extends Model
{
    use ValidatingTrait;

    protected $rules = [
		'name'   => 'required',
        'type' => 'required',
	];

    protected $fillable = [
        'name', 
        'description',
        'type',
    ];

    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => ucwords($value),
        );
    }
}