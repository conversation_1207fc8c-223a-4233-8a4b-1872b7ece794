<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Watson\Validating\ValidatingTrait;

class Post extends Model
{
    use ValidatingTrait;

    protected $rules = [
		'title'   => 'required',
        'description' => 'required',
        'slug' => 'required',
        'content' => 'required',
        'cover' => 'required',
        'topic_id' => 'required',
        'category_id' => 'required',
        'summary' => 'required',
	];

    protected $fillable = [
        'title', 
        'description', 
        'slug', 
        'content', 
        'cover', 
        'topic_id', 
        'category_id',
        'user_id',
        'summary',
    ];

    protected $casts = [
        'summary' => 'array',
    ];

    public function topic()
    {
        return $this->belongsTo(Topic::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}