<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Watson\Validating\ValidatingTrait;

class PostArticle extends Model
{
    use ValidatingTrait;

    protected $rules = [
		'post_id'   => 'required',
        'article_id' => 'required',
	];

    protected $fillable = [
        'post_id', 
        'article_id',
    ];

    public function post()
    {
        return $this->belongsTo(Post::class);
    }

    public function article()
    {
        return $this->belongsTo(Article::class);
    }
}
