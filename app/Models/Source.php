<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Watson\Validating\ValidatingTrait;

class Source extends Model
{
    use ValidatingTrait;

    protected $rules = [
		'name'   => 'required',
        'url' => 'required',
        'avatar' => 'required',
        'crawling_url' => 'required',
	];

    protected $fillable = [
        'name', 
        'url',
        'avatar',
        'crawling_url',
    ];
}
