<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Watson\Validating\ValidatingTrait;

class Topic extends Model
{
    use ValidatingTrait;

    protected $rules = [
		'name'   => 'required',
        'slug' => 'required',
        'photo' => 'required',
	];

    protected $fillable = [
        'name', 
        'slug',
        'color',
        'photo',
        'parent_id',
    ];

    public function parent()
    {
        return $this->belongsTo(Topic::class, 'parent_id');
    }
}
