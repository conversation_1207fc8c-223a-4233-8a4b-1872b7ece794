<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Watson\Validating\ValidatingTrait;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Laravel\Scout\Searchable;

class Topic extends Model
{
    use ValidatingTrait;
    use Searchable;

    public $asYouType = true;

    protected $rules = [
		'name'   => 'required',
        'slug' => 'required',
	];

    protected $fillable = [
        'name', 
        'slug',
        'color',
        'photo',
        'parent_id',
        'in_use',
    ];

    protected $casts = [
        'in_use' => 'boolean',
    ];

    public function parent()
    {
        return $this->belongsTo(Topic::class, 'parent_id');
    }

    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => ucfirst($value),
            set: fn (string $value) => ucfirst($value),
        );
    }

    public function toSearchableArray()
    {
        return $this->toArray();
    }
}
