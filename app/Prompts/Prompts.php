<?php

namespace App\Prompts;

class Prompts
{
    public static function getArticle(string $title, string $text) 
    {
        return <<<EOT
            Response sempre neste formato JSON:

            {
                "title": {
                    "params": {
                        "clickbait": 0,
                        "sensacionalismo": 0,
                        "neutralidade": 0
                    },
                    "reasons": {
                        "clickbait": "",
                        "sensacionalismo": "",
                        "neutralidade": ""
                    },
                    "topics": []
                },
                "text": {
                    "params": {
                        "clareza": 0,
                        "enviesamento": 0,
                        "rigor": 0
                    },
                    "reasons:" {
                        "clareza": "",
                        "enviesamento": "",
                        "rigor": ""
                    },
                    "summary": [],
                    "entities": []   
                }
            }

            Substitui os valores de acordo com as instruções. Não adicione comentários, nem explicações fora do JSON.

            Agora, considerando o objecto title, interpreta o título "$title" e numa escala com até duas casas decimais entre 0 e 1, classifica os seguintes parâmetros: clickbait, sensacionalismo e neutralidade. Adiciona ao object params e depois fornece SEMPRE uma explicação do valor da classificação dada a cada parâmetro no object reasons. Coloca SEMPRE no array topics, apenas o melhor tópico para este título usando até cinco palavras (não mais!) de preferência com um substantivo e verbo ou identificando uma pessoa, um evento ou uma ocasião.

            Por fim, considerando o object text, interpreta o texto "$text" e numa escala com até duas casas decimais entre 0 e 1, classifica os seguintes parâmetros: clareza, enviesamento e rigor do autor. Adiciona ao object params e depois fornce SEMPRE uma explicação do valor da classificação dada a cada parâmetro no object reasons. Resume o texto com neutralidade em até quatro pontos e coloca em summary. Identifica pessoas e organizações no texto e coloca no array entities.
            EOT;
    }

    public static function getSuggestedCategory(string $str, array $categoriesArr) 
    {
        $list = join('\n', $categoriesArr);

        return <<<EOT
            Response sempre neste formato JSON:

            {
                "category": ""
            }

            De acordo com a lista seguinte, tenta encontrar uma categoria que se ajuste a "$str" e responda na chave category.\n\n Lista: $list
            EOT;
    }

    public static function getSuggestedTopic(string $str)
    {
        return <<<EOT
            Response sempre neste formato JSON:

            {
                "topic": ""
            }

            Com base na seguinte string "$str", devolve um tópico bem formatado em até 5 palavras (não mais!) na chave topic.
            EOT;
    }

    public static function identifyTopic(string $title, array $topics)
    {
        $list = join('\n', $topics);

        return <<<EOT
            Response sempre neste formato JSON:

            {
                "topic": ""
            }

            De acordo com a lista seguinte, tenta encontrar um tópico que se ajuste a "$title" e responda na chave topic.\n\n Lista: $list
            EOT;
    }
}

