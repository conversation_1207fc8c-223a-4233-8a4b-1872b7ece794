<?php

namespace App\Services;

use App\Models\Category;
use App\Prompts\Prompts;
use OpenAI\Laravel\Facades\OpenAI;

class OpenAIChat
{
    public function __construct(
        readonly private Category $categoryModel,
    ) {
        
    }

    public static function chat(array $messages, $model = 'gpt-3.5-turbo')
    {
        try {
            $result = OpenAI::chat()->create([
                'model' => $model,
                'messages' => $messages,
            ]);

            return $result;
        } catch (\Exception $e) {
            dump($e->getMessage());
        }

        return null;
    }

    public static function getSuggestedCategory(string $piece, array $categoriesMap)
    {
        $category = null;

        $categoriesArr = \App\Models\Category::get()
            ->map(fn($item) => $item->name)
            ->toArray();

        $messages = [
            [
                'role' => 'system',
                'content' => 'Você é um assistente que utiliza o idioma português de Portugal e responde SEMPRE no formato JSON especificado.',
            ],
            [
                'role' => 'user',
                'content' => Prompts::getSuggestedCategory($piece, $categoriesArr)
            ],
        ];

        try {
            $response = self::chat($messages);
            $output = json_decode($response->choices[0]->message->content);

            if ((isset($output->category)) && (in_array($output->category, $categoriesArr))) {
                $textCategory = $output->category;
                if (isset($categoriesMap[$textCategory])) {
                    $category = \App\Models\Category::find($categoriesMap[$textCategory]);
                }
            }
        } catch (\Exception $e) {
            
        }

        return $category;
    }

    public static function getSuggestedTopic(string $str)
    {
        $messages = [
            [
                'role' => 'system',
                'content' => 'Você é um assistente que utiliza o idioma português de Portugal e responde SEMPRE no formato JSON especificado.',
            ],
            [
                'role' => 'user',
                'content' => Prompts::getSuggestedTopic($str)
            ],
        ];

        try {
            $response = self::chat($messages);
            $output = json_decode($response->choices[0]->message->content);

            if (isset($output->topic)) {
                return $output->topic;
            }
        } catch (\Exception $e) {
            
        }

        return null;
    }

    public static function identifyTopic(string $title, array $topics)
    {
        $messages = [
            [
                'role' => 'system',
                'content' => 'Você é um assistente que utiliza o idioma português de Portugal e responde SEMPRE no formato JSON especificado.',
            ],
            [
                'role' => 'user',
                'content' => Prompts::identifyTopic($title, $topics)
            ],
        ];

        try {
            $response = self::chat($messages);
            $output = json_decode($response->choices[0]->message->content);

            if (isset($output->topic)) {
                return $output->topic;
            }
        } catch (\Exception $e) {
            
        }

        return null;
    }
}
