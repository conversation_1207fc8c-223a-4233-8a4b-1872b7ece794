<?php

namespace App\Services;

use OpenAI\Laravel\Facades\OpenAI;

class OpenAIChat
{
    public static function chat(array $messages)
    {
        try {
            $result = OpenAI::chat()->create([
                'model' => 'gpt-3.5-turbo',
                'messages' => $messages,
            ]);

            return $result;
        } catch (\Exception $e) {
            dump($e->getMessage());
        }

        return null;
    }
}
