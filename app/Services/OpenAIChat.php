<?php

namespace App\Services;

use Exception;
use OpenAI\Laravel\Facades\OpenAI;

class OpenAIChat
{
    public function request(array $messages)
    {
        try {
            $result = OpenAI::chat()->create([
                'model' => 'gpt-4o-mini',
                'messages' => $messages,
            ]);

            return $result;
        } catch (\Exception $e) {
            dump($e->getMessage());
        }

        return null;
    }
}
