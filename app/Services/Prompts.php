<?php

namespace App\Services;

class Prompts
{
    public const SYSTEM_ANALYST = [
        ['role' => 'system', 'content' => 'You are an expert data analyst. Analyze the provided data and extract key insights, trends, and patterns. Provide clear, actionable conclusions.']
    ];

    public const SYSTEM_SUMMARIZER = [
        ['role' => 'system', 'content' => 'You are a professional content summarizer. Create concise, accurate summaries that capture the main points and key information from the provided text.']
    ];

    public const SYSTEM_NEWS_CLASSIFIER = [
        ['role' => 'system', 'content' => 'You are a news classification expert. Categorize news articles by topic (politics, sports, technology, health, business, entertainment, etc.) and determine their sentiment (positive, negative, neutral).']
    ];

    public const SYSTEM_FACT_CHECKER = [
        ['role' => 'system', 'content' => 'You are a fact-checking assistant. Analyze statements and claims for accuracy, identify potential misinformation, and provide evidence-based assessments.']
    ];

    public const SYSTEM_TRANSLATOR = [
        ['role' => 'system', 'content' => 'You are a professional translator. Translate text accurately while preserving meaning, tone, and context. Specify the source and target languages clearly.']
    ];

    public const SYSTEM_CONTENT_MODERATOR = [
        ['role' => 'system', 'content' => 'You are a content moderation specialist. Review content for inappropriate material, spam, hate speech, or policy violations. Provide clear reasoning for your assessments.']
    ];

    public const SYSTEM_SEO_OPTIMIZER = [
        ['role' => 'system', 'content' => 'You are an SEO expert. Optimize content for search engines by suggesting keywords, meta descriptions, titles, and content improvements while maintaining readability.']
    ];

    public const SYSTEM_JSON_EXTRACTOR = [
        ['role' => 'system', 'content' => 'You are a data extraction specialist. Extract structured information from unstructured text and return it in valid JSON format. Be precise and consistent with your data structure.']
    ];

    /**
     * Create a user message
     */
    public static function user(string $content): array
    {
        return ['role' => 'user', 'content' => $content];
    }

    /**
     * Create an assistant message
     */
    public static function assistant(string $content): array
    {
        return ['role' => 'assistant', 'content' => $content];
    }

    /**
     * Create a system message
     */
    public static function system(string $content): array
    {
        return ['role' => 'system', 'content' => $content];
    }

    /**
     * Combine system prompt with user message
     */
    public static function withSystem(array $systemPrompt, string $userMessage): array
    {
        return array_merge($systemPrompt, [self::user($userMessage)]);
    }

    /**
     * Create a conversation with context
     */
    public static function conversation(array $systemPrompt, array $messages): array
    {
        return array_merge($systemPrompt, $messages);
    }

    /**
     * News analysis prompt
     */
    public static function analyzeNews(string $newsContent): array
    {
        return self::withSystem(
            self::SYSTEM_NEWS_CLASSIFIER,
            "Analyze this news article and provide: 1) Main category, 2) Sentiment analysis, 3) Key topics, 4) Summary. News content: {$newsContent}"
        );
    }

    /**
     * Content summarization prompt
     */
    public static function summarize(string $content, int $maxWords = 100): array
    {
        return self::withSystem(
            self::SYSTEM_SUMMARIZER,
            "Summarize the following content in maximum {$maxWords} words: {$content}"
        );
    }

    /**
     * Data extraction to JSON prompt
     */
    public static function extractToJson(string $content, string $schema = ''): array
    {
        $instruction = "Extract structured data from the following content and return as valid JSON";
        if ($schema) {
            $instruction .= " following this schema: {$schema}";
        }
        $instruction .= ". Content: {$content}";

        return self::withSystem(self::SYSTEM_JSON_EXTRACTOR, $instruction);
    }

    /**
     * Fact checking prompt
     */
    public static function factCheck(string $statement): array
    {
        return self::withSystem(
            self::SYSTEM_FACT_CHECKER,
            "Fact-check this statement and provide an assessment with evidence: {$statement}"
        );
    }

    /**
     * Translation prompt
     */
    public static function translate(string $text, string $fromLang, string $toLang): array
    {
        return self::withSystem(
            self::SYSTEM_TRANSLATOR,
            "Translate the following text from {$fromLang} to {$toLang}: {$text}"
        );
    }
}
