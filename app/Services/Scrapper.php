<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class Scrapper
{
    private Client $client;
    private array $defaultHeaders;
    private int $timeout;

    public function __construct()
    {
        
    }

    /**
     * Make a POST request to the specified URL and return JSON response
     *
     * @param string $url The URL to send the POST request to
     * @param array $data The data to send in the POST request body
     * @param array $headers Additional headers to send with the request
     * @return array|null Returns the JSON response as an array if successful, null otherwise
     */
    public function postRequest(string $url, array $data = [], array $headers = []): ?array
    {
        try {
            $response = $this->client->post($url, [
                'json' => $data,
                'headers' => array_merge($this->defaultHeaders, $headers)
            ]);

            // Check if the status code is 200 OK
            if ($response->getStatusCode() === 200) {
                $body = $response->getBody()->getContents();

                // Decode JSON response
                $jsonData = json_decode($body, true);

                // Check if JSON decoding was successful
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $jsonData;
                } else {
                    Log::error('Failed to decode JSON response', [
                        'url' => $url,
                        'json_error' => json_last_error_msg(),
                        'response_body' => $body
                    ]);
                    return null;
                }
            } else {
                Log::warning('Unexpected status code received', [
                    'url' => $url,
                    'status_code' => $response->getStatusCode(),
                    'response_body' => $response->getBody()->getContents()
                ]);
                return null;
            }

        } catch (RequestException $e) {
            Log::error('HTTP request failed', [
                'url' => $url,
                'error' => $e->getMessage(),
                'status_code' => $e->hasResponse() ? $e->getResponse()->getStatusCode() : null,
                'response_body' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            return null;

        } catch (GuzzleException $e) {
            Log::error('Guzzle exception occurred', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('Unexpected error during POST request', [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Set custom timeout for requests
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function setTimeout(int $timeout): void
    {
        $this->timeout = $timeout;
        $this->client = new Client([
            'timeout' => $this->timeout,
            'connect_timeout' => 10,
            'headers' => $this->defaultHeaders
        ]);
    }

    /**
     * Set custom headers for all requests
     *
     * @param array $headers Headers to set
     * @return void
     */
    public function setHeaders(array $headers): void
    {
        $this->defaultHeaders = array_merge($this->defaultHeaders, $headers);
        $this->client = new Client([
            'timeout' => $this->timeout,
            'connect_timeout' => 10,
            'headers' => $this->defaultHeaders
        ]);
    }
}