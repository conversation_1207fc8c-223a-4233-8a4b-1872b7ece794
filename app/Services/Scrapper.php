<?php

namespace App\Services;

use Exception;
use GuzzleHttp\Client;

class Scrapper
{
    private Client $client;
    private string $token;

    public function __construct()
    {
        $this->client = new Client();   
        $this->token = config('services.scrapper.token');
    }

    public function post(string $url)
    {
        try {
            $response = $this->client->post($url);

            if ($response->getStatusCode() === 200) {
                $body = $response->getBody()->getContents();

                $jsonData = json_decode($body);

                if (json_last_error() === JSON_ERROR_NONE) {
                    return $jsonData;
                } else {
                    return $body;
                }
            }
        } catch (Exception $e) {

        }

        return false;
    }

    public function get(string $url, $useScrapperDo = false)
    {
        try {
            if ($useScrapperDo) {
                $url = "http://api.scrape.do/?url=" . urlencode($url) . "&token=" . $this->token;
            }

            $response = $this->client->get($url);

            if ($response->getStatusCode() === 200) {
                $body = $response->getBody()->getContents();

                $jsonData = json_decode($body);

                if (json_last_error() === JSON_ERROR_NONE) {
                    return $jsonData;
                } else {
                    return $body;
                }
            }
        } catch (Exception $e) {
            # dump($e->getMessage());
        }

        return false;
    }
}