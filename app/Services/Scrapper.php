<?php

namespace App\Services;

use Exception;
use GuzzleHttp\Client;

class Scrapper
{
    private Client $client;
    private string $token;

    public function __construct()
    {
        $this->client = new Client();   
        $this->token = config('services.scrapper.token');
    }

    public function post(string $url): ?array
    {
        try {
            $response = $this->client->post($url);

            if ($response->getStatusCode() === 200) {
                $body = $response->getBody()->getContents();

                return json_decode($body, true);
            }
        } catch (Exception $e) {

        }

        return null;
    }

    public function get(string $url, $useScrapperDo = false): ?array
    {
        try {
            if ($useScrapperDo) {
                $url = "http://api.scrape.do/?url=" . urlencode($url) . "&token=" . $this->token;
            }

            $response = $this->client->get($url);

            if ($response->getStatusCode() === 200) {
                $body = $response->getBody()->getContents();

                return json_decode($body, true);
            }
        } catch (Exception $e) {

        }

        return null;
    }
}