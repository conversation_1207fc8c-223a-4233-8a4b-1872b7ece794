<?php

namespace App\Services;

use Exception;
use GuzzleHttp\Client;

class Scrapper
{
    private Client $client;
    private string $token;

    public function __construct()
    {
        $this->client = new Client();   
        $this->token = config('services.scrapper.token');
    }

    public function post(string $url): ?array
    {
        try {
            $response = $this->client->post($url);

            if ($response->getStatusCode() === 200) {
                $body = $response->getBody()->getContents();

                return json_decode($body, true);
            }
        } catch (Exception $e) {

        }

        return null;
    }

    public function get(string $url, $useScrapperDo = false): ?array
    {
        try {
            if ($useScrapperDo) {
                $url = "http://api.scrape.do/?url=" . urlencode($url) . "&token=" . $this->token;
            }

            $response = $this->client->get($url);

            if ($response->getStatusCode() === 200) {
                $body = $response->getBody()->getContents();

                return json_decode($body, true);
            }
        } catch (Exception $e) {

        }

        return null;
    }

    public function fetchAll(array $urls, $useScrapperDo = false): array
    {
        $results = [];

        foreach ($urls as $url) {
            $result = $this->get($url, $useScrapperDo);
            $results[] = [
                'url' => $url,
                'data' => $result,
                'success' => $result !== null
            ];
        }

        return $results;
    }

    public function process(array $data): array
    {
        $processed = [];

        foreach ($data as $item) {
            if (isset($item['success']) && $item['success'] && isset($item['data'])) {
                $processed[] = [
                    'url' => $item['url'] ?? null,
                    'processed_data' => $this->processItem($item['data']),
                    'timestamp' => now()->toISOString()
                ];
            }
        }

        return $processed;
    }

    private function processItem(array $data): array
    {
        // Basic processing - can be customized based on needs
        return [
            'item_count' => count($data),
            'keys' => array_keys($data),
            'has_data' => !empty($data),
            'original' => $data
        ];
    }
}