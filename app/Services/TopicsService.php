<?php

namespace App\Services;

use App\Models\Topic;

class TopicsService
{
    public function __construct(
        readonly private Topic $topic
    ) {

    }

    public function getAll()
    {
        return $this->topic
            ->query()
            ->get();
    }

    public function getBySlug(string $slug)
    {
        return $this->topic
            ->query()
            ->where('slug', $slug)
            ->first();
    }
}
