<?php

namespace App\Sources\SicNoticias;

use App\Services\Scrapper;
use App\Models\Source;
use PHPHtmlParser\Dom;
use App\Jobs\ProcessArticle;
use App\Models\Category;
use App\Models\Topic;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Services\OpenAIChat;

class SicNoticias
{
    use DispatchesJobs;

    private const CATEGORIES_MAP = [
        'País' => 5,
        'Cultura' => 8,
        'Mundo' => 6,
        'Desporto' => 3,
        'Saúde e Bem-estar' => 9,
        'Economia' => 2,
        'Auto' => 10,
        'Meteorologia' => 5,
        'Tecnologia' => 7
    ];

    public function __construct(
        readonly private Scrapper $scrapper,
        readonly private Category $categoryModel,
        readonly private Topic $topicModel,
    ) {}

    public function handle(Source $source): void
    {
        foreach ($source->crawling_url as $crawlingUrl) {
            $html = $this->fetchSourceUrl($crawlingUrl, false); // should be true

            if (!$html) {
                return;
            }

            $dom = new Dom();
            $dom->load($html);

            foreach ($dom->find('ul.list-articles.latest') as $listArticles) {                
                $lis = $listArticles->find('li');                

                foreach ($lis as $li) {
                    $title = $li->find('h2.title');                    

                    if (!sizeof($title)) {
                        break;
                    }

                    $aLink = $li->find('h2.title')[0]->find('a');

                    if (!sizeof($aLink)) {
                        break;
                    }

                    $href = $aLink[0]->getAttribute('href');

                    if (empty($href)) {
                        break;
                    }

                    $url = $this->getCleanUrl($source->url, $href);                    

                    $category = $li->find('.category');
                    if (sizeof($category)) {
                        $aCategory = $category->find('a');
                        $textCategory = trim($aCategory->text);

                        $pieces = array_values(array_filter(explode('/', $aCategory->getAttribute('href'))));

                        $category = null;

                        if (sizeof($pieces) == 1) {
                            if (isset(self::CATEGORIES_MAP[$textCategory])) {
                                $category = $this->categoryModel->find(self::CATEGORIES_MAP[$textCategory]);
                            }
                        } else {
                            foreach ($pieces as $key => $piece) {
                                if (in_array($piece, ['podcasts', 'programas'])) {
                                    break;
                                }

                                if ($key == (sizeof($pieces) - 1)) {
                                    $category = OpenAIChat::getSuggestedCategory($piece, self::CATEGORIES_MAP);
                                }
                            }
                        }

                        if ($category) {
                            // $this->processUrl($source, $category, $url);
                        }
                    }

                    sleep(1);

                    # break;
                }
            }
        }
    }

    public function fetchSourceUrl(string $url, bool $useScrapperDo)
    {
        return $this->scrapper->get($url, $useScrapperDo);
    }

    public function processUrl(Source $source, Category $category, string $url): void
    {
        $html = $this->fetchSourceUrl($url, false); // should be true
        
        $dom = new Dom();
        $dom->load($html);

        $meta = \Kovah\HtmlMeta\Facades\HtmlMeta::fromHtml($html)->getMeta();

        $content = [];

        foreach ($dom->find('.full-article-body') as $articleBody) {
            foreach ($articleBody->find('p,blockquote') as $element) {
                switch ($element->tag->name()) {
                    case 'p': {
                            if ($element->getParent()->tag->name() !== 'blockquote') {
                                $stripTags = trim(strip_tags($element->innerHtml));

                                if (!empty($stripTags)) {
                                    $content[] = [
                                        'fromTag' => 'p',
                                        'text' => $stripTags,
                                        'html' => $element->innerHtml,
                                    ];
                                }
                            }

                            break;
                        }
                    case 'blockquote': {
                            foreach ($element->find('p') as $p) {
                                $stripTags = trim(strip_tags($p->innerHtml));

                                if (!empty($stripTags)) {
                                    $content[] = [
                                        'fromTag' => 'blockquote',
                                        'text' => $stripTags,
                                        'html' => $p->innerHtml,
                                    ];
                                }
                            }
                        }
                    default:
                        break;
                }
            }
        }

        $this->dispatch(new ProcessArticle($meta, $content, $source, $category));
    }

    private function getCleanUrl(string $baseUrl, string $url): string
    {
        $parsedUrl = parse_url($url);

        return !isset($parsedUrl['host']) ? $baseUrl . $parsedUrl['path'] : ($parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $parsedUrl['path']);
    }
}
