<?php

namespace App\Sources\SicNoticias;

use App\Services\Scrapper;
use App\Models\Source;
use PHPHtmlParser\Dom;
use App\Services\OpenAIChat;
use App\Jobs\ProcessArticle;
use Illuminate\Foundation\Bus\DispatchesJobs;

class SicNoticias
{
    use DispatchesJobs;

    public function __construct(
        readonly private Scrapper $scrapper,
        readonly private Source $source
    ) {}

    public function handle(Source $source)
    {
        $html = $this->fetchSourceUrl($source->crawling_url, false); // should be true

        $dom = new Dom();
        $dom->load($html);

        foreach ($dom->find('.list-articles') as $listArticle) {
            foreach ($listArticle->find('p.lead') as $h2Title) {
                if (!isset($h2Title->find('a')[0])) {
                    continue;
                }

                $this->processUrl($source->crawling_url, $h2Title->find('a')[0]->getAttribute('href'));
                break;
            }
        }
    }

    public function fetchSourceUrl(string $url, bool $useScrapperDo)
    {
        return $this->scrapper->get($url, $useScrapperDo);
    }

    public function processUrl(string $sourceUrl, string $url)
    {
        $parseUrl = parse_url($sourceUrl);
        $baseUrl = $parseUrl['scheme'] . '://' . $parseUrl['host'];
        $url = $baseUrl . $url;

        $url = 'https://sicnoticias.pt/pais/2025-08-02-portuguesa-que-procurava-os-filhos-no-peru-anuncia-rafael-e-felipe-ja-estao-comigo-novamente-e2df2a1c';

        $html = $this->fetchSourceUrl($url, false); // should be true

        $dom = new Dom();
        $dom->load($html);

        $meta = \Kovah\HtmlMeta\Facades\HtmlMeta::fromHtml($html)->getMeta();

        $content = [];

        foreach ($dom->find('.full-article-body') as $articleBody) {
            foreach ($articleBody->find('p,blockquote') as $element) {
                switch ($element->tag->name()) {
                    case 'p': {
                        if ($element->getParent()->tag->name() !== 'blockquote') {
                            $stripTags = trim(strip_tags($element->innerHtml));

                            if (!empty($stripTags)) {
                                $content[] = [
                                    'fromTag' => 'p',
                                    'text' => $stripTags,
                                    'html' => $element->innerHtml,
                                ];
                            }
                        }

                        break;
                    }
                    case 'blockquote': {
                        foreach ($element->find('p') as $p) {
                            $stripTags = trim(strip_tags($p->innerHtml));

                            if (!empty($stripTags)) {
                                $content[] = [
                                    'fromTag' => 'blockquote',
                                    'text' => $stripTags,
                                    'html' => $p->innerHtml,
                                ];
                            }
                        }
                    }
                    default:
                        break;
                }
            }
        }

        $this->dispatch(new ProcessArticle($meta, $content));
    }    
}
