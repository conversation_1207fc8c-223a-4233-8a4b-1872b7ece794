<?php

namespace App\Sources\SicNoticias;

use App\Services\Scrapper;
use App\Models\Source;
use PHPHtmlParser\Dom;

class SicNoticias
{
    public function __construct(
        readonly private Scrapper $scrapper,
        readonly private Source $source
    ) {
        
    }

    public function handle(Source $source)
    {
        $html = $this->fetchSourceUrl($source->crawling_url, false); // should be true
        $dom = new Dom();
        $dom->load($html);

        foreach($dom->find('.list-articles') as $listArticle) {
            foreach ($listArticle->find('p.lead') as $h2Title) {
                if (!isset($h2Title->find('a')[0])) {
                    continue;
                }

                $this->processUrl($source->crawling_url, $h2Title->find('a')[0]->getAttribute('href'));
            }   
        }        
    }

    public function fetchSourceUrl(string $url, bool $useScrapperDo)
    {
        return $this->scrapper->get($url, $useScrapperDo);   
    }

    public function processUrl(string $sourceUrl, string $url)
    {
        $parseUrl = parse_url($sourceUrl);
        $baseUrl = $parseUrl['scheme'] . '://' . $parseUrl['host'];
        $url = $baseUrl . $url;

        $html = $this->fetchSourceUrl($url, false); // should be true

        $nlpUrl = "http://host.docker.internal:6400/newspaper";
        $post_data = ['html' => $html];
    }    
}