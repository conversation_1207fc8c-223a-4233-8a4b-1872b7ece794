<?php

namespace App\Sources\SicNoticias;

use App\Services\Scrapper;
use App\Models\Source;

class SicNoticias
{
    public function __construct(
        readonly private Scrapper $scrapper,
        readonly private Source $source
    ) {
        
    }

    public function handle(Source $source)
    {
        $items = $this->fetchAll($source->crawling_url, true);

        if ($items) {

        }
    }

    public function fetchAll($url, $useScrapperDo)
    {
        return $this->scrapper->get($url, $useScrapperDo);   
    }

    public function process()
    {
        
    }    
}