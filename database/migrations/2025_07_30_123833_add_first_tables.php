<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();

            $table->string('name');
            $table->enum('type', ['SOURCE', 'TOPIC', 'ARTICLE']);

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('sources', function (Blueprint $table) {
            $table->id();

            $table->string('name');
            $table->string('url');
            $table->string('avatar');
            $table->string('crawling_url');

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('topics', function (Blueprint $table) { 
            $table->id();

            $table->string('name');
            $table->string('slug')->unique();
            $table->string('color')->nullable();
            $table->string('photo');

            $table->timestamps();
            $table->softDeletes();
        });        

        Schema::table('topics', function (Blueprint $table) {             
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id')->references('id')->on('topics')->onDelete('cascade');
        });

        Schema::create('posts', function (Blueprint $table) { // artigo interno, criado a partir de outros articles
            $table->id();

            $table->string('title');
            $table->text('description');
            $table->string('slug');
            $table->longText('content');
            $table->string('cover');
            $table->json('summary');

            $table->foreignId('topic_id')->constrained()->cascadeOnDelete();
            $table->foreignId('category_id')->constrained()->cascadeOnDelete();

            $table->foreignId('user_id')->nullable()->constrained()->cascadeOnDelete();

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('articles', function (Blueprint $table) { // artigos externos
            $table->id();
            
            $table->string('title');
            $table->text('description');
            $table->string('url');
            $table->longText('content');
            $table->string('cover');
            $table->string('slug');
            $table->json('summary');

            $table->foreignId('source_id')->constrained()->cascadeOnDelete();
            $table->foreignId('category_id')->constrained()->cascadeOnDelete();
            $table->foreignId('topic_id')->constrained()->cascadeOnDelete();            

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('posts_articles', function (Blueprint $table) {
            $table->id();

            $table->foreignId('post_id')->constrained()->cascadeOnDelete();
            $table->foreignId('article_id')->constrained()->cascadeOnDelete();

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('articles_changes', function (Blueprint $table) { 
            $table->id();

            $table->foreignId('article_id')->constrained()->cascadeOnDelete();
            $table->longText('content');

            $table->timestamps();
            $table->softDeletes();
        });

        // ['enviesamento (bias % of conclusions)', 'title.clickbait', 'imparcialidade', 'sentimental analysis or social media analysis', 'alpha analysis', 'text originality', 'source identification', 'manipulations', 'photo reverse search', 'video reverse search']
        Schema::create('parameters', function (Blueprint $table) {
            $table->id();

            $table->string('name');
            $table->string('description');
            $table->enum('type', ['TITLE', 'TEXT']);

            $table->timestamps();
            $table->softDeletes();
        });
        
        
        Schema::create('articles_parameters', function (Blueprint $table) {
            $table->id();

            $table->foreignId('article_id')->constrained()->cascadeOnDelete();
            $table->foreignId('parameter_id')->constrained()->cascadeOnDelete();

            $table->float('value');

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('entities', function (Blueprint $table) {
            $table->id();

            $table->string('name');
            $table->enum('type', ['PERSON', 'ORGANIZATION']);
            $table->string('photo');

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('articles_entities', function (Blueprint $table) {
            $table->id();

            $table->foreignId('entity_id')->constrained()->cascadeOnDelete();
            $table->foreignId('article_id')->constrained()->cascadeOnDelete();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('articles_entities', function (Blueprint $table) {
            $table->dropForeign(['entity_id']);
            $table->dropForeign(['article_id']);
        });   
        Schema::dropIfExists('articles_entities');
        
        Schema::dropIfExists('entities');

        Schema::table('articles_parameters', function (Blueprint $table) {
            $table->dropForeign(['article_id']);
            $table->dropForeign(['parameter_id']);
        });   
        Schema::dropIfExists('articles_parameters');

        Schema::dropIfExists('parameters');

        Schema::table('articles_changes', function (Blueprint $table) {
            $table->dropForeign(['article_id']);
        });   
        Schema::dropIfExists('articles_changes');

        Schema::table('posts_articles', function (Blueprint $table) {
            $table->dropForeign(['post_id']);
            $table->dropForeign(['article_id']);
        });   
        Schema::dropIfExists('posts_articles');

        Schema::table('articles', function (Blueprint $table) {
            $table->dropForeign(['source_id']);
            $table->dropForeign(['category_id']);
            $table->dropForeign(['topic_id']);
        });   
        Schema::dropIfExists('articles');        

        Schema::table('posts', function (Blueprint $table) {
            $table->dropForeign(['topic_id']);
            $table->dropForeign(['category_id']);
            $table->dropForeign(['user_id']);
        });   
        Schema::dropIfExists('posts');

        Schema::table('topics', function (Blueprint $table) {
            $table->dropForeign(['parent_id']);
        });   
        Schema::dropIfExists('topics');

        Schema::dropIfExists('sources');
        Schema::dropIfExists('categories');
    }
};
