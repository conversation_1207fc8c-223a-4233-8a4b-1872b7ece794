<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SourcesSeeder extends Seeder
{
    public function run(): void
    {

        DB::table('sources')->insert(
            [
                [
                    'name' => 'SIC Notícias',
                    'url' => 'https://sicnoticias.pt',
                    'avatar' => 'sicnoticias.png',
                    'crawling_url' => json_encode([
                        'https://sicnoticias.pt/ultimas',
                    ]),
                ],                    
                [
                    'name' => 'Notícias ao Minuto',
                    'url' => 'https://www.noticiasaominuto.com',
                    'avatar' => 'naom.png',
                    'crawling_url' => json_encode([
                        'https://www.noticiasaominuto.com/rss/politica',
                        'https://www.noticiasaominuto.com/rss/economia',
                        'https://www.noticiasaominuto.com/rss/desporto',
                        'https://www.noticiasaominuto.com/rss/fama',
                        'https://www.noticiasaominuto.com/rss/pais',
                        'https://www.noticiasaominuto.com/rss/mundo',
                        'https://www.noticiasaominuto.com/rss/tech',
                        'https://www.noticiasaominuto.com/rss/cultura',
                        'https://www.noticiasaominuto.com/rss/lifestyle',
                        'https://www.noticiasaominuto.com/rss/auto',
                    ]),
                ],
                [
                    'name' => 'Expresso',
                    'url' => 'https://expresso.pt/',
                    'avatar' => 'expresso.png',
                    'crawling_url' => json_encode([
                        'https://expresso.pt/ultimas',
                    ]),
                ]
            ]
        );
    }
}
