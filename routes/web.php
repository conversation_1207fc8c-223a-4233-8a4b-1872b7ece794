<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    /*
    $url = 'https://www.sapo.pt/noticias/atualidade/area-ardida-antes-dos-incendios-da-ultima_688b241f1b3aef62094f06da';
    $curlUrl = 'http://host.docker.internal:6400/newspaper?url=' . $url;

    $response = file_get_contents($curlUrl);

    if (!empty($response)) {
        dump(json_decode($response, true));
    }
    */

    $url = 'https://www.sapo.pt/noticias/atualidade/camara-de-faro-partilha-dados-de_688bb047972d596203b2fe68';
    $html = file_get_contents($url);

    $url = "http://host.docker.internal:6400/newspaper";
    $post_data = ['html' => $html];


    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));    
    $response = curl_exec($ch);

    if (!empty($response)) {
        dump(json_decode($response, true));
    }

    return '';
});
