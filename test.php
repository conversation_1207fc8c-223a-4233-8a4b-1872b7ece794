<?php
// python3 nlpserver.py
// java -mx3g -cp "/opt/local/share/java/stanford-corenlp/*" edu.stanford.nlp.pipeline.StanfordCoreNLPServer -port 9000 -timeout 15000
require __DIR__.'/vendor/autoload.php';

/*
$corenlp = new \Web64\Nlp\CoreNlp('http://localhost:9000');
$entities = $corenlp->entities( 'Woman arrested on suspicion of setting fire in Arouca' );
var_dump($entities);
*/

$url = 'https://www.sapo.pt/noticias/atualidade/area-ardida-antes-dos-incendios-da-ultima_688b241f1b3aef62094f06da';

$nlp = new \Web64\Nlp\NlpClient('http://localhost:6400/');

//$html = file_get_contents( $url );
$newspaper = $nlp->newspaper($url);
var_dump($newspaper);

$curlUrl = 'http://localhost:6400/newspaper?url=' . $url;

$response = file_get_contents($curlUrl);

if (!empty($response)) {
    var_dump(json_decode($response, true));
}

/*
$text = "Harvesters is a 1905 oil painting on canvas by the Danish artist Anna Ancher, a member of the artists' community known as the Skagen Painters.";
$entities = $nlp->spacy_entities( $text );
var_dump($entities);
*/